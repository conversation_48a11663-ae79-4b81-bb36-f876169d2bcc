# 需求说明
    在一个excel文件中，指定某一列的序号，统计该列中各个揽投部是名称出现的次数，并汇总到该揽投部所属的单位，然后输出到一个新的excel文件中，该文件包含单位名称，该单位包含的揽投部出现的次数，单位的顺序：
    城区营业部，电商中心，政务中心，商企中心，北湖区，苏仙区，桂阳县，宜章县，永兴县，嘉禾县，临武县，汝城县，桂东县，安仁县，资兴市。
# 单位名称以及揽投部名称归属单位的规则
1. 城区营业部
揽投部名称包含以下字眼的都统计到该单位去：
开发区，白鹿洞，火车站，南塔，北湖，骆仙，石榴湾，王仙岭，五岭直投
2. 网路运营中心
    包含以下字眼的都统计到该单位去：
    网路运营
3. 电商中心
    包含以下字眼的都统计到该单位去：
    万华路1
4. 政务中心
    包含以下字眼的都统计到该单位去：
    万华路2     
5. 商企中心
    包含以下字眼的都统计到该单位去：
    万华路3
6. 北湖区
     包含以下字眼的都统计到该单位去：
     石盖塘，华塘，鲁塘
7. 苏仙区
    包含以下字眼的都统计到该单位去：
    白露塘、良田、栖凤渡、坳上、许家洞、五里牌
8. 以下11个单位的匹配原则是：只要揽投部名称中含有以下区县名称的就归到该单位去。
    桂阳县
    宜章县
    永兴县
    嘉禾县
    临武县
    汝城县
    桂东县
    安仁县
    资兴市
vs code 启动虚拟环境的办法：
1. 在 VS Code 中打开终端(Terminal)
2. 选择 PowerShell 终端
3. 按照以下步骤操作：psshell打开权限： Set-ExecutionPolicy -Scope Process -ExecutionPolicy RemoteSigned
4. 启动虚拟环境说明：先进入虚拟环境目录：cd .venv\Scripts\   然后激活虚拟环境：.\Activate.ps1
 
