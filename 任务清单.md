- 收寄逾限通报
   - 写文案：根据各个揽投部的逾期件数进行通报。通报每个揽投部的逾期件数。模板如下：【时限整治专项行动-收寄逾限情况通报】-截至{当天的日期}日08:30
全市超0小时及以上逾限邮件共计12件，其中：南塔4件、苏仙区2件、桂阳3件、宜章1件、汝城1件、桂东1件；请各单位对超时邮件逐件核查，立即处理完毕。


# 收寄逾限自动处理
1. 读取逾限数据excel格式
    - 逾期数据文件在：D:\2025年运管工作\抖音菜鸟指数\收寄逾限通报\yyyymmdd\在途邮件分环节逾限情况监控-有收寄无出口发运yyyymmdd.xls
    - 揽投机构名称数据位置：在第C列，从第5行开始取数揽投机构名称，直到没有数据为止。
    - 按照以下原则匹配归属到揽投部，每出现一次计数一下数表示该揽投部多了一件逾期邮件，为接下来功能做好数据准备：
        unit_rules = {
            '火车站揽投部': ['火车站'],
            '南塔揽投部': ['南塔'],
            '开发区揽投部': ['开发区'],
            '石榴湾揽投部': ['石榴湾'],
            '骆仙揽投部': ['骆仙'],
            '白鹿洞揽投部': ['白鹿洞'],
            '北湖揽投部': ['北湖'],
            '王仙岭揽投部': ['王仙岭'],
            '电商中心': ['电商中心'],
            '北湖区': ['石盖塘', '华塘', '鲁塘'],
            '苏仙区': ['白露塘', '良田', '栖凤渡', '坳上', '许家洞', '五里牌'],
            '桂阳县': ['桂阳县'],
            '宜章县': ['宜章县'],
            '永兴县': ['永兴县'],
            '嘉禾县': ['嘉禾县'],
            '临武县': ['临武县'],
            '汝城县': ['汝城县'],
            '桂东县': ['桂东县'],
            '安仁县': ['安仁县'],
            '资兴市': ['资兴市']
        }

2. 生产通报文案
    根据各个揽投部的逾期件数进行通报。通报每个揽投部的逾期件数。模板如下：【时限整治专项行动-收寄逾限情况通报】-截至日期{当天的日期格式：mm.dd}{上午显示8:30，下午显示：14:30}
全市超0小时及以上逾限邮件共计12件，其中：{}内是示例，{南塔4件、苏仙区2件、桂阳3件、宜章1件、汝城1件、桂东1件；}请各单位对超时邮件逐件核查，立即处理完毕。
3. 生产通报excle文件
    
4. 在微信群里直接发送1
# 抖音菜鸟通报自动生成
