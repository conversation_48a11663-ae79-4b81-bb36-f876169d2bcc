def douyin_cainiao_statistics():
    """抖音菜鸟揽投部汇总统计"""
    print("请粘贴你的数据（每行一个揽投部名称），输入完成后输入'END'结束：")
    
    data_lines = []
    while True:
        line = input()
        if line.strip().upper() == 'END':
            break
        if line.strip():  # 只添加非空行
            data_lines.append(line)
    
    data_text = '\n'.join(data_lines)
    
    # 处理数据
    lines = data_text.strip().split('\n')
  #TODO 抖音单位匹配规则  
    # 定义单位匹配规则
    unit_rules = {
        '城区营业部': ['开发区', '白鹿洞', '火车站', '南塔', '北湖', '骆仙', '石榴湾', '王仙岭', '五岭直投'],
        '网路运营中心': ['网路运营', '郴州邮件处理车间'],
        '电商中心': ['万华路1'],
        '政务中心': ['万华路2'],
        '商企中心': ['万华路3'],
        '北湖区': ['石盖塘', '华塘', '鲁塘','永春'],
        '苏仙区': ['白露塘', '良田', '栖凤渡', '坳上', '许家洞', '五里牌','桥口','五盖山','马头岭','廖家湾','柿竹园'],
        '桂阳县': ['桂阳县'],
        '宜章县': ['宜章县'],
        '永兴县': ['永兴县'],
        '嘉禾县': ['嘉禾县'],
        '临武县': ['临武县'],
        '汝城县': ['汝城县'],
        '桂东县': ['桂东县'],
        '安仁县': ['安仁县'],
        '资兴市': ['资兴市']
    }
    
    # 统计各单位次数
    unit_counts = {unit: 0 for unit in unit_rules.keys()}
    
    # 遍历数据进行匹配
    for line in lines:
        line = line.strip()
        if line:
            for unit, keywords in unit_rules.items():
                if any(keyword in line for keyword in keywords):
                    unit_counts[unit] += 1
                    break
    
    # 按指定顺序输出
    ordered_units = ['城区营业部', '网路运营中心', '电商中心', '政务中心', '商企中心', '北湖区', '苏仙区', 
                    '桂阳县', '宜章县', '永兴县', '嘉禾县', '临武县', '汝城县', '桂东县', '安仁县', '资兴市']
    
    # 计算总数
    total_count = sum(unit_counts[unit] for unit in ordered_units)
    
    # 输出数量
    print()
    for unit in ordered_units:
        count = unit_counts[unit]
        print('-' if count == 0 else count)
    print(total_count)

def shoujiyuxian_statistics():
    """收寄逾限归属统计"""
    print("请粘贴你的数据（每行一个揽投部名称），输入完成后直接按回车结束：")
    
    data_lines = []
    while True:
        line = input()
        if line.strip() == '':
            break
        data_lines.append(line)
    
    data_text = '\n'.join(data_lines)
    
    # 处理数据
    lines = data_text.strip().split('\n')
    
    # 定义单位匹配规则
    unit_rules = {
        '火车站揽投部': ['火车站'],
        '南塔揽投部': ['南塔'],
        '开发区揽投部': ['开发区'],
        '石榴湾揽投部': ['石榴湾'],
        '骆仙揽投部': ['骆仙'],
        '白鹿洞揽投部': ['白鹿洞'],
        '北湖揽投部': ['北湖'],
        '王仙岭揽投部': ['王仙岭'],
        '电商中心': ['电商中心'],
        '北湖区': ['石盖塘', '华塘', '鲁塘'],
        '苏仙区': ['白露塘', '良田', '栖凤渡', '坳上', '许家洞', '五里牌'],
        '桂阳县': ['桂阳县'],
        '宜章县': ['宜章县'],
        '永兴县': ['永兴县'],
        '嘉禾县': ['嘉禾县'],
        '临武县': ['临武县'],
        '汝城县': ['汝城县'],
        '桂东县': ['桂东县'],
        '安仁县': ['安仁县'],
        '资兴市': ['资兴市']
    }
    
    # 统计各单位次数
    unit_counts = {unit: 0 for unit in unit_rules.keys()}
    
    # 遍历数据进行匹配
    for line in lines:
        line = line.strip()
        if line:
            for unit, keywords in unit_rules.items():
                if any(keyword in line for keyword in keywords):
                    unit_counts[unit] += 1
                    break
    
    # 按指定顺序输出
    ordered_units = ['火车站揽投部', '南塔揽投部', '开发区揽投部', '石榴湾揽投部', '骆仙揽投部', 
                    '白鹿洞揽投部', '北湖揽投部', '王仙岭揽投部', '电商中心', '北湖区', '苏仙区',
                    '桂阳县', '宜章县', '永兴县', '嘉禾县', '临武县', '汝城县', '桂东县', '安仁县', '资兴市']
    
    # 计算总数
    total_count = sum(unit_counts[unit] for unit in ordered_units)
    
    # 输出数量
    print()
    for unit in ordered_units:
        count = unit_counts[unit]
        print('-' if count == 0 else count)
    print(total_count)

def show_menu():
    """显示菜单"""
    print("\n=== 数据处理工具 ===")
    print("1. 抖音菜鸟揽投部汇总统计")
    print("2. 收寄逾限归属统计")
    print("0. 退出")
    print("=" * 20)

def main():
    """主程序"""
    while True:
        show_menu()
        choice = input("请选择功能（输入数字）: ").strip()
        
        if choice == '1':
            douyin_cainiao_statistics()
        elif choice == '2':
            shoujiyuxian_statistics()
        elif choice == '0':
            print("程序已退出")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()

