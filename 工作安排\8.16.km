{"root": {"data": {"id": "dc3opz5dtk00", "created": 1755330473355, "text": "MainTopic"}, "children": [{"data": {"id": "dc3oq6qwm1c0", "created": 1755330489893, "text": "browse mcp server 自动采集数据"}, "children": []}, {"data": {"id": "dc3orzvs5cw0", "created": 1755330631679, "text": "利用playwright框架编程采集数据"}, "children": []}, {"data": {"id": "dc3ot2u5rfc0", "created": 1755330716475, "text": "mcp client"}, "children": []}]}, "template": "right", "theme": "fresh-blue", "version": "1.4.43"}