# 收寄逾限需求文档功能修改和扩充
## 读取逾期邮件数据
1. 当前时间为上午读取‘收寄在局邮件逾限(新）(0830)xlsx'
2. 当前时间为下午读取‘收寄在局邮件逾限(新）(1430)xlsx'
## 通报文案生产
1. 删除模板里的“（压降目标0件）”
2. 除了显示处理，还要生成文本文件。文件名称为：收寄逾限情况通报-yyyymmdd-0830/1430.txt，上午为0830，下午为1430，内容为生成的文案。
## Excel通报生成
1. 除了显示处理，还要生成Excel文件。文件名称为：收寄逾限情况通报-yyyymmdd-0830/1430.xlsx，上午为0830，下午为1430。
2. 模板的第一行内容写入后为：时限整治专项行动-收寄逾限情况通报(截至:8月1日 08:30),其中：8月1日 08:30为变量，需要替换为当前日期，上午为08:30，下午为14:30。
# 微信通报文案模板
- 抖音收寄逾限微信模板
  王仙岭抖音收寄逾限，请及时处理，以免影响抖音平台指标。
