if __name__ == "__main__":
    print("请粘贴你的数据（每行一个揽投部名称），输入完成后直接按回车结束：")
    
    data_lines = []
    while True:
        line = input()
        if line.strip() == '':
            break
        data_lines.append(line)
    
    data_text = '\n'.join(data_lines)
    
    # 处理数据
    lines = data_text.strip().split('\n')
    
    # 定义单位匹配规则
    unit_rules = {
        '城区营业部': ['开发区', '白鹿洞', '火车站', '南塔', '北湖', '骆仙', '石榴湾', '王仙岭', '五岭直投'],
        '网路运营中心': ['网路运营'],
        '电商中心': ['万华路1'],
        '政务中心': ['万华路2'],
        '商企中心': ['万华路3'],
        '北湖区': ['石盖塘', '华塘', '鲁塘'],
        '苏仙区': ['白露塘', '良田', '栖凤渡', '坳上', '许家洞', '五里牌'],
        '桂阳县': ['桂阳县'],
        '宜章县': ['宜章县'],
        '永兴县': ['永兴县'],
        '嘉禾县': ['嘉禾县'],
        '临武县': ['临武县'],
        '汝城县': ['汝城县'],
        '桂东县': ['桂东县'],
        '安仁县': ['安仁县'],
        '资兴市': ['资兴市']
    }
    
    # 统计各单位次数
    unit_counts = {unit: 0 for unit in unit_rules.keys()}
    
    # 遍历数据进行匹配
    for line in lines:
        line = line.strip()
        if line:
            for unit, keywords in unit_rules.items():
                if any(keyword in line for keyword in keywords):
                    unit_counts[unit] += 1
                    break
    
    # 按指定顺序输出
    ordered_units = ['城区营业部', '电商中心', '政务中心', '商企中心', '北湖区', '苏仙区', 
                    '桂阳县', '宜章县', '永兴县', '嘉禾县', '临武县', '汝城县', '桂东县', '安仁县', '资兴市']
    
    # 计算总数
    total_count = sum(unit_counts[unit] for unit in ordered_units)
    
    # 输出markdown格式
    print("\n| 单位名称 | 出现次数 |")
    print("| --- | --- |")
    for unit in ordered_units:
        print(f"| {unit} | {unit_counts[unit]} |")
    print(f"| 总计 | {total_count} |")

if __name__ == "__main__":
    print("请粘贴你的数据（每行一个揽投部名称），输入完成后按回车并输入'END'结束：")
    
    data_lines = []
    while True:
        line = input()
        if line.strip().upper() == 'END':
            break
        data_lines.append(line)
    
    data_text = '\n'.join(data_lines)
    process_data(data_text)












