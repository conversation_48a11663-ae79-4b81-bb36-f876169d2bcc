import pandas as pd
from datetime import datetime
import os
import glob

def read_yuxian_excel_from_path(date_str=None):
    """根据日期读取逾限数据Excel文件 - 增强调试版本"""
    if date_str is None:
        date_str = datetime.now().strftime("%Y%m%d")
    
    # 根据当前时间确定文件名
    now = datetime.now()
    if now.hour < 12:
        time_suffix = "0830"
    else:
        time_suffix = "1430"
    
    # 构建文件路径
    base_path = r"D:\2025年运管工作\收寄逾限通报"
    file_pattern = f"{base_path}\\{date_str}\\收寄在局邮件逾限{time_suffix}.xlsx"
    
    print(f"=== Excel文件读取调试信息 ===")
    print(f"文件路径: {file_pattern}")
    print(f"文件存在: {os.path.exists(file_pattern)}")
    
    try:
        # 1. 先读取原始数据查看结构
        print("\n1. 读取原始Excel结构:")
        df_raw = pd.read_excel(file_pattern, header=None)
        print(f"   原始文件形状: {df_raw.shape}")
        print(f"   前10行C列和D列数据:")
        for i in range(min(10, len(df_raw))):
            c_value = df_raw.iloc[i, 2] if len(df_raw.columns) > 2 else "N/A"
            d_value = df_raw.iloc[i, 3] if len(df_raw.columns) > 3 else "N/A"
            print(f"     行{i+1}: C列={repr(c_value)}, D列={repr(d_value)}")
        
        # 2. 按指定参数读取C列和D列数据
        print(f"\n2. 按参数读取 (skiprows=3, usecols=[2,3]):")
        df = pd.read_excel(file_pattern, skiprows=3, usecols=[2, 3])
        print(f"   处理后形状: {df.shape}")
        print(f"   列名: {list(df.columns)}")
        
        # 3. 过滤只保留C列为"郴州市"的行
        print(f"\n3. 过滤郴州市数据:")
        c_col = df.iloc[:, 0]  # C列
        d_col = df.iloc[:, 1]  # D列
        
        # 找到C列为"郴州市"的行
        chenzhou_mask = c_col.astype(str).str.contains('郴州市', na=False)
        filtered_d_data = d_col[chenzhou_mask].dropna().tolist()
        
        print(f"   原始总行数: {len(df)}")
        print(f"   郴州市行数: {chenzhou_mask.sum()}")
        print(f"   过滤后有效数据: {len(filtered_d_data)}")
        
        # 4. 详细分析过滤后的数据
        print(f"\n4. 过滤后数据分析:")
        for i, value in enumerate(filtered_d_data, 1):
            print(f"     {i}. {repr(value)}")
        
        # 5. 检查重复数据
        print(f"\n5. 重复数据检查:")
        from collections import Counter
        data_counts = Counter(filtered_d_data)
        duplicates = {k: v for k, v in data_counts.items() if v > 1}
        if duplicates:
            print(f"   发现重复数据:")
            for data, count in duplicates.items():
                print(f"     '{data}' 出现 {count} 次")
        else:
            print(f"   无重复数据")
        
        # 6. 桂阳县数据专项检查
        print(f"\n6. 桂阳县数据专项检查:")
        guiyang_data = [data for data in filtered_d_data if '桂阳县' in str(data)]
        print(f"   桂阳县相关数据共 {len(guiyang_data)} 条:")
        for i, data in enumerate(guiyang_data, 1):
            print(f"     {i}. {data}")
        
        print(f"\n=== 读取完成 ===")
        print(f"最终返回数据: {len(filtered_d_data)} 条")
        
        return filtered_d_data
        
    except FileNotFoundError:
        print(f"文件不存在: {file_pattern}")
        return None
    except Exception as e:
        print(f"读取Excel文件出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def count_unit_overdue_debug(data_list):
    """统计各揽投部逾期件数 - 调试增强版"""
    #TODO 投递逾限单位匹配规则
    unit_rules = {
        '火车站揽投部': ['火车站'],
        '南塔揽投部': ['南塔'],
        '开发区揽投部': ['开发区'],
        '石榴湾揽投部': ['石榴湾'],
        '骆仙揽投部': ['骆仙'],
        '白鹿洞揽投部': ['白鹿洞'],
        '北湖揽投部': ['北湖'],
        '王仙岭揽投部': ['王仙岭'],
        '电商中心': ['电商中心'],
        '北湖区': ['石盖塘', '华塘', '鲁塘','永春'],
        '苏仙区': ['白露塘', '良田', '栖凤渡', '坳上', '许家洞', '五里牌','桥口','五盖山','马头岭','廖家湾','柿竹园'],
        '桂阳县': ['桂阳县'],
        '宜章县': ['宜章县'],
        '永兴县': ['永兴县'],
        '嘉禾县': ['嘉禾县'],
        '临武县': ['临武县'],
        '汝城县': ['汝城县'],
        '桂东县': ['桂东县'],
        '安仁县': ['安仁县'],
        '资兴市': ['资兴市']
    }
    
    print(f"\n=== 统计过程调试 ===")
    print(f"输入数据总数: {len(data_list)}")
    # 初始化计数器
    unit_counts = {unit: 0 for unit in unit_rules.keys()}
    guiyang_matches = []  # 专门记录桂阳县匹配情况
    
    # 统计每个揽投部的逾期件数
    for i, data in enumerate(data_list, 1):
        data_str = str(data).strip()
        if data_str:
            print(f"{i}. 处理: {data_str}")
            matched = False
            for unit, keywords in unit_rules.items():
                if any(keyword in data_str for keyword in keywords):
                    unit_counts[unit] += 1
                    print(f"   ✓ 匹配到 {unit}")
                    if unit == '桂阳县':
                        guiyang_matches.append((i, data_str))
                    matched = True
                    break
            if not matched:
                print(f"   ✗ 未匹配")
    
    print(f"\n=== 桂阳县匹配详情 ===")
    print(f"桂阳县匹配总数: {len(guiyang_matches)}")
    for idx, data in guiyang_matches:
        print(f"  第{idx}条: {data}")
    
    return unit_counts

def count_specific_institutions(data_list):
    """统计D列中每个具体揽投机构的出现次数"""
    print(f"\n=== 具体机构统计调试 ===")
    print(f"输入数据总数: {len(data_list)}")
    
    # 统计每个具体机构的出现次数
    institution_counts = {}
    for i, data in enumerate(data_list, 1):
        data_str = str(data).strip()
        if data_str:
            # 清理机构名称，只保留到"揽投部"为止
            if '揽投部' in data_str:
                clean_name = data_str.split('揽投部')[0] + '揽投部'
            else:
                clean_name = data_str
        
            # 统计计数
            if clean_name in institution_counts:
                institution_counts[clean_name] += 1
            else:
                institution_counts[clean_name] = 1
            
            print(f"{i}. 原始: {data_str}")
            print(f"   清理后: {clean_name}")
    
    # 按件数排序
    sorted_institutions = sorted(institution_counts.items(), key=lambda x: x[1], reverse=True)
    print(f"\n=== 机构统计结果 ===")
    for institution, count in sorted_institutions:
        print(f"  {institution}: {count}件")
    
    return sorted_institutions

def generate_report_text(unit_counts, data_list=None, custom_date=None):
    """生成通报文案 - 包含单位统计和突出机构"""
    now = datetime.now()
    
    if custom_date:
        date_str = custom_date
    else:
        # 根据当前时间决定显示上午还是下午时间
        if now.hour < 12:
            time_str = "08:30"
        else:
            time_str = "14:30"
        date_str = f"{now.month}月{now.day}日{time_str}"
    
    # 过滤出有逾期件数的单位并按件数从多到少排序
    overdue_units = {unit: count for unit, count in unit_counts.items() if count > 0}
    sorted_units = sorted(overdue_units.items(), key=lambda x: x[1], reverse=True)
    
    # 计算总件数
    total_count = sum(overdue_units.values())
    
    # 生成单位件数描述（按件数排序）
    unit_descriptions = []
    for unit, count in sorted_units:
        # 简化单位名称显示
        display_name = unit.replace('揽投部', '').replace('县', '').replace('市', '').replace('区', '')
        unit_descriptions.append(f"{display_name}{count}件")
    
    # 构建通报文案主体（删除"压降目标0件"）
    report_text = f"【时限整治专项行动-收寄逾限情况通报】-截至{date_str}\n"
    report_text += f"全市超时逾限邮件{total_count}件其中："
    report_text += "、".join(unit_descriptions)
    report_text += "；"
    
    # 添加突出机构信息（如果提供了原始数据）
    if data_list:
        print(f"\n=== 生成突出机构信息 ===")
        # 统计具体机构的出现次数
        sorted_institutions = count_specific_institutions(data_list)
        
        if sorted_institutions:
            report_text += "\n⚠⚠⚠突出机构:"
            top_5_institutions = sorted_institutions[:5]  # 取前5名
            for institution, count in top_5_institutions:
                report_text += f"\n*{institution}{count}件"
    
    # 添加结尾文案
    report_text += "\n各单位要对超时邮件件核查，滞留邮件按频次全部发运完成，实现超时逾限邮件清零。"
    
    return report_text

def test_with_sample_data():
    """使用示例数据测试"""
    print("=== 使用示例数据测试 ===")
    
    # 示例数据
    sample_data = [
        "南塔揽投部001", "南塔揽投部002", "南塔揽投部003", "南塔揽投部004",
        "苏仙区白露塘", "苏仙区良田",
        "桂阳县001", "桂阳县002", "桂阳县003",
        "宜章县001",
        "汝城县001",
        "桂东县001"
    ]
    
    print("示例数据:")
    for data in sample_data:
        print(f"  - {data}")
    
    # 统计逾期件数
    unit_counts = count_unit_overdue_debug(sample_data)
    
    print("\n统计结果:")
    for unit, count in unit_counts.items():
        if count > 0:
            print(f"  {unit}: {count}件")
    
    # 生成通报文案（包含突出机构）
    report_text = generate_report_text(unit_counts, sample_data)
    print(f"\n生成的通报文案:\n{report_text}")

def test_with_real_data():
    """使用真实数据测试"""
    print("=== 使用真实数据测试 ===")
    
    # 你提供的真实数据
    real_data = [
        "桂阳县茶红路揽投部（三合一）(42446300)",
        "桂阳县洋市邮政支局(42441301)",
        "桂阳县樟市邮政支局(42442301)",
        "桂阳县樟市邮政支局(42442301)",
        "桂阳县樟市邮政支局(42442301)"
    ]
    
    print("真实数据:")
    for i, data in enumerate(real_data, 1):
        print(f"  {i}. {data}")
    
    print(f"\n数据总数: {len(real_data)}")
    
    # 统计逾期件数
    unit_counts = count_unit_overdue_debug(real_data)
    
    print("\n统计结果:")
    for unit, count in unit_counts.items():
        if count > 0:
            print(f"  {unit}: {count}件")
    
    # 生成通报文案（包含突出机构）
    report_text = generate_report_text(unit_counts, real_data)
    print(f"\n生成的通报文案:\n{report_text}")

def compare_excel_vs_manual():
    """对比Excel读取数据与手动数据的差异"""
    print("=== Excel数据 vs 手动数据对比测试 ===")
    
    # 手动数据
    manual_data = [
        "桂阳县茶红路揽投部（三合一）(42446300)",
        "桂阳县洋市邮政支局(42441301)",
        "桂阳县樟市邮政支局(42442301)",
        "桂阳县樟市邮政支局(42442301)",
        "桂阳县樟市邮政支局(42442301)"
    ]
    
    print("1. 手动数据测试:")
    manual_counts = count_unit_overdue_debug(manual_data)
    
    print("\n" + "="*50)
    
    print("2. Excel数据测试:")
    excel_data = read_yuxian_excel_from_path()
    if excel_data:
        excel_counts = count_unit_overdue_debug(excel_data)
        
        print(f"\n=== 对比结果 ===")
        print(f"手动数据桂阳县: {manual_counts['桂阳县']}件")
        print(f"Excel数据桂阳县: {excel_counts['桂阳县']}件")
        print(f"差异: {manual_counts['桂阳县'] - excel_counts['桂阳县']}件")

def generate_excel_report():
    """生成收寄逾期通报Excel文件"""
    # 1. 构建模板文件路径 - 在当天日期目录下
    current_date = datetime.now().strftime("%Y%m%d")
    base_path = r"D:\2025年运管工作\收寄逾限通报"
    template_path = f"{base_path}\\{current_date}\\收寄逾限情况通报模板.xlsx"
    
    print("=== 生成Excel通报文件 ===")
    print(f"读取模板文件: {template_path}")
    
    try:
        # 检查模板文件是否存在
        if not os.path.exists(template_path):
            print(f"模板文件不存在: {template_path}")
            return None
        
        # 2. 获取当前逾期数据
        print(f"\n获取逾期数据...")
        overdue_data = read_yuxian_excel_from_path()
        if not overdue_data:
            print("无法获取逾期数据")
            return None
        
        # 3. 统计各单位逾期件数
        print(f"\n统计各单位逾期件数...")
        unit_counts = count_unit_overdue_debug(overdue_data)
        
        # 4. 过滤并排序数据
        overdue_units = {unit: count for unit, count in unit_counts.items() if count > 0}
        sorted_units = sorted(overdue_units.items(), key=lambda x: x[1], reverse=True)
        
        if not sorted_units:
            print("没有逾期数据需要通报")
            return None
        
        # 5. 生成输出文件名 - 也在同一目录下
        output_filename = f"收寄逾期通报_{current_date}.xlsx"
        output_path = f"{base_path}\\{current_date}\\{output_filename}"
        
        # 6. 使用openpyxl处理Excel文件
        from openpyxl import load_workbook
        from openpyxl.styles import Font
        
        # 加载模板文件
        wb = load_workbook(template_path)
        ws = wb.active
        
        # 7. 从第4行开始插入数据
        start_row = 4
        current_row = start_row
        
        # 插入各单位数据（按逾限量从大到小排序）
        for i, (unit, count) in enumerate(sorted_units):
            ws.cell(row=current_row, column=1, value=unit)
            ws.cell(row=current_row, column=2, value=count)
            
            # 设置字体格式
            if i < 5:  # 前5个单位特殊格式
                # 红色加粗字体
                red_bold_font = Font(size=11, bold=True, color="FF0000")
                ws.cell(row=current_row, column=1).font = red_bold_font
                ws.cell(row=current_row, column=2).font = red_bold_font
            else:
                # 普通11号字体
                normal_font = Font(size=11)
                ws.cell(row=current_row, column=1).font = normal_font
                ws.cell(row=current_row, column=2).font = normal_font
            
            current_row += 1
        
        # 8. 添加合计行
        total_count = sum(count for _, count in sorted_units)
        ws.cell(row=current_row, column=1, value="合计")
        ws.cell(row=current_row, column=2, value=total_count)
        
        # 合计行使用普通格式
        normal_font = Font(size=11)
        ws.cell(row=current_row, column=1).font = normal_font
        ws.cell(row=current_row, column=2).font = normal_font
        
        # 9. 保存文件
        wb.save(output_path)
        wb.close()
        
        print(f"\n✓ Excel文件生成成功!")
        print(f"文件路径: {output_path}")
        print(f"包含 {len(sorted_units)} 个单位的逾期数据")
        print(f"逾期邮件总数: {total_count} 件")
        
        # 显示生成的数据预览
        print(f"\n数据预览（按逾限量排序）:")
        for i, (unit, count) in enumerate(sorted_units[:5]):  # 显示前5个
            status = "【红色加粗】" if i < 5 else ""
            print(f"  {i+1}. {unit}: {count}件 {status}")
        if len(sorted_units) > 5:
            print(f"  ... 还有 {len(sorted_units)-5} 个单位（普通格式）")
        print(f"  合计: {total_count}件 【普通格式】")
        
        return output_path
        
    except Exception as e:
        print(f"生成Excel文件出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_overdue_report(date_str=None, output_dir=None):
    """综合逾限通报功能 - 生成文案和Excel文件"""
    print("=== 逾限通报功能（文案+Excel）===")
    
    # 1. 确定日期和时间后缀
    now = datetime.now()
    if date_str is None:
        date_str = now.strftime("%Y%m%d")
        print(f"使用当天日期: {date_str}")
    else:
        print(f"使用指定日期: {date_str}")
    
    # 确定时间后缀
    if now.hour < 12:
        time_suffix = "0830"
        time_display = "08:30"
    else:
        time_suffix = "1430"
        time_display = "14:30"
    
    # 2. 读取逾限数据
    print(f"\n步骤1: 读取逾限数据...")
    overdue_data = read_yuxian_excel_from_path(date_str)
    if not overdue_data:
        print("❌ 无法获取逾限数据，通报生成失败")
        return None
    
    print(f"✓ 成功读取 {len(overdue_data)} 条逾限数据")
    
    # 3. 统计各单位逾期件数
    print(f"\n步骤2: 统计各单位逾期件数...")
    unit_counts = count_unit_overdue_debug(overdue_data)
    
    # 过滤出有逾期件数的单位
    overdue_units = {unit: count for unit, count in unit_counts.items() if count > 0}
    if not overdue_units:
        print("❌ 没有逾期数据需要通报")
        return None
    
    total_count = sum(overdue_units.values())
    print(f"✓ 统计完成，共 {len(overdue_units)} 个单位，总计 {total_count} 件逾期邮件")
    
    # 4. 生成通报文案
    print(f"\n步骤3: 生成通报文案...")
    report_text = generate_report_text(unit_counts, overdue_data)
    print(f"✓ 通报文案生成完成")
    
    # 5. 保存文案到文本文件
    print(f"\n步骤4: 保存文案到文本文件...")
    if output_dir is None:
        base_path = r"D:\2025年运管工作\收寄逾限通报"
        output_dir = f"{base_path}\\{date_str}"
    
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存文案到文本文件
    txt_filename = f"收寄逾限情况通报-{date_str}-{time_suffix}.txt"
    txt_path = f"{output_dir}\\{txt_filename}"
    
    try:
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(report_text)
        print(f"✓ 文案已保存到: {txt_path}")
    except Exception as e:
        print(f"❌ 文案保存失败: {e}")
        txt_path = None
    
    # 6. 生成Excel通报文件
    print(f"\n步骤5: 生成Excel通报文件...")
    
    # 检查模板文件
    template_path = f"{output_dir}\\收寄逾限情况通报模板.xlsx"
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        print("Excel文件生成失败，但文案已生成")
        excel_path = None
    else:
        try:
            # 生成Excel文件
            from openpyxl import load_workbook
            from openpyxl.styles import Font, Alignment
            from openpyxl.utils import get_column_letter
            
            # 排序单位数据
            sorted_units = sorted(overdue_units.items(), key=lambda x: x[1], reverse=True)
            
            # 统计突出机构
            sorted_institutions = count_specific_institutions(overdue_data)
            
            # 生成输出文件名
            excel_filename = f"收寄逾限情况通报-{date_str}-{time_suffix}.xlsx"
            excel_path = f"{output_dir}\\{excel_filename}"
            
            # 加载模板文件
            wb = load_workbook(template_path)
            ws = wb.active
            
            # 更新第一行标题
            date_obj = datetime.strptime(date_str, "%Y%m%d")
            title_text = f"时限整治专项行动-收寄逾限情况通报(截至:{date_obj.month}月{date_obj.day}日 {time_display})"
            ws.cell(row=1, column=1, value=title_text)
            
            # 从第4行开始插入数据
            current_row = 4
            
            # 插入各单位数据（按逾限量从大到小排序）
            for i, (unit, count) in enumerate(sorted_units):
                ws.cell(row=current_row, column=1, value=unit)
                ws.cell(row=current_row, column=2, value=count)
                
                # 设置字体格式
                if i < 5:  # 前5个单位红色加粗
                    red_bold_font = Font(size=11, bold=True, color="FF0000")
                    ws.cell(row=current_row, column=1).font = red_bold_font
                    ws.cell(row=current_row, column=2).font = red_bold_font
                else:
                    normal_font = Font(size=11)
                    ws.cell(row=current_row, column=1).font = normal_font
                    ws.cell(row=current_row, column=2).font = normal_font
                
                current_row += 1
            
            # 添加合计行（红色加粗）
            ws.cell(row=current_row, column=1, value="合计")
            ws.cell(row=current_row, column=2, value=total_count)
            red_bold_font = Font(size=11, bold=True, color="FF0000")
            ws.cell(row=current_row, column=1).font = red_bold_font
            ws.cell(row=current_row, column=2).font = red_bold_font
            current_row += 1
            
            # 添加突出机构部分
            if sorted_institutions:
                # 空行
                current_row += 1
                
                # 突出机构标题（红色加粗，合并单元格并居中）
                ws.merge_cells(f'A{current_row}:B{current_row}')
                ws.cell(row=current_row, column=1, value="突出机构")
                red_bold_font = Font(size=11, bold=True, color="FF0000")
                center_alignment = Alignment(horizontal='center', vertical='center')
                ws.cell(row=current_row, column=1).font = red_bold_font
                ws.cell(row=current_row, column=1).alignment = center_alignment
                current_row += 1
                
                # 添加前5个突出机构（红色加粗，合并单元格并居中）
                top_5_institutions = sorted_institutions[:5]
                for institution, count in top_5_institutions:
                    institution_text = f"{institution} {count}件"
                    ws.merge_cells(f'A{current_row}:B{current_row}')
                    ws.cell(row=current_row, column=1, value=institution_text)
                    red_bold_font = Font(size=11, bold=True, color="FF0000")
                    center_alignment = Alignment(horizontal='center', vertical='center')
                    ws.cell(row=current_row, column=1).font = red_bold_font
                    ws.cell(row=current_row, column=1).alignment = center_alignment
                    current_row += 1
            
            # 保存文件
            wb.save(excel_path)
            wb.close()
            
            print(f"✓ Excel文件生成成功: {excel_path}")
            
        except Exception as e:
            print(f"❌ Excel文件生成失败: {e}")
            import traceback
            traceback.print_exc()
            excel_path = None
    
    # 7. 输出结果
    print(f"\n" + "="*60)
    print(f"📋 逾限通报生成完成")
    print(f"="*60)
    print(f"📅 通报日期: {date_str}")
    print(f"⏰ 时间标识: {time_suffix}")
    print(f"📊 逾期总数: {total_count} 件")
    print(f"🏢 涉及单位: {len(overdue_units)} 个")
    
    if txt_path:
        print(f"📄 文案文件: {txt_path}")
    else:
        print(f"📄 文案文件: 保存失败")
        
    if excel_path:
        print(f"📁 Excel文件: {excel_path}")
    else:
        print(f"📁 Excel文件: 生成失败")
    
    print(f"\n📝 通报文案:")
    print(f"-" * 60)
    print(report_text)
    print(f"-" * 60)
    
    # 返回结果
    result = {
        'date': date_str,
        'time_suffix': time_suffix,
        'total_count': total_count,
        'unit_count': len(overdue_units),
        'report_text': report_text,
        'txt_path': txt_path,
        'excel_path': excel_path,
        'success': True
    }
    
    return result

def main():
    """主测试函数"""
    while True:
        print("\n=== 收寄逾限通报生成测试 ===")
        print("1. 使用示例数据测试")
        print("2. 使用真实数据测试")
        print("3. 读取今天的Excel文件")
        print("4. 读取指定日期的Excel文件")
        print("5. Excel vs 手动数据对比调试")
        print("6. 生成Excel通报文件")
        print("7. 逾限通报功能（文案+Excel）")
        print("0. 退出")
        
        choice = input("请选择: ").strip()
        
        if choice == '1':
            test_with_sample_data()
        elif choice == '2':
            test_with_real_data()
        elif choice == '3':
            data = read_yuxian_excel_from_path()  # 读取今天的Excel文件
            if data:
                unit_counts = count_unit_overdue_debug(data)
                report_text = generate_report_text(unit_counts, data)  # 传入原始数据
                print(f"\n通报文案:\n{report_text}")
        elif choice == '4':
            date_input = input("请输入日期(格式: 20250115): ").strip()
            if len(date_input) == 8 and date_input.isdigit():
                data = read_yuxian_excel_from_path(date_input)
                if data:
                    unit_counts = count_unit_overdue_debug(data)
                    report_text = generate_report_text(unit_counts, data)  # 传入原始数据
                    print(f"\n通报文案:\n{report_text}")
            else:
                print("日期格式错误，请使用YYYYMMDD格式")
        elif choice == '5':
            compare_excel_vs_manual()
        elif choice == '6':
            generate_excel_report()
        elif choice == '7':
            # 新的综合功能
            date_choice = input("使用当天日期？(y/n，默认y): ").strip().lower()
            if date_choice == 'n':
                date_input = input("请输入日期(格式: 20250115): ").strip()
                if len(date_input) == 8 and date_input.isdigit():
                    generate_overdue_report(date_input)
                else:
                    print("日期格式错误，请使用YYYYMMDD格式")
            else:
                generate_overdue_report()
        elif choice == '0':
            break
        else:
            print("无效选择")


if __name__ == "__main__":
    main()















