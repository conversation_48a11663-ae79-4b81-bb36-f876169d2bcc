import pandas as pd
import os

def check_excel_dependencies():
    """检查Excel相关依赖"""
    try:
        import openpyxl
        return True
    except ImportError:
        print("错误: 缺少openpyxl依赖")
        print("请运行: pip install openpyxl")
        return False

def analyze_excel_structure(file_path):
    """分析Excel文件结构"""
    # if not check_excel_dependencies():
    #     return
        
    try:
        # 获取所有工作表名称
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"文件: {file_path}")
        print(f"工作表数量: {len(sheet_names)}")
        print("工作表名称:")
        for i, sheet in enumerate(sheet_names):
            print(f"  {i}: {sheet}")
        
        # 分析每个工作表的结构
        for sheet_name in sheet_names:
            print(f"\n--- 工作表: {sheet_name} ---")
            df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=5)  # 只读前5行
            print(f"行数: {len(df)} (显示前5行)")
            print(f"列数: {len(df.columns)}")
            print("列名:")
            for i, col in enumerate(df.columns):
                print(f"  {i}: {col}")
            print("\n数据预览:")
            print(df.head())
            print("-" * 50)
            
    except Exception as e:
        print(f"分析文件出错: {e}")

def read_excel_columns(file_path, sheet_name=0, columns=None, start_row=0, max_rows=None):
    """读取Excel文件指定列的数据"""
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return None
        
        # 读取数据
        df = pd.read_excel(
            file_path, 
            sheet_name=sheet_name, 
            usecols=columns,
            skiprows=start_row,
            nrows=max_rows
        )
        
        print(f"成功读取数据: {len(df)} 行, {len(df.columns)} 列")
        return df
        
    except Exception as e:
        print(f"读取文件出错: {e}")
        return None

def extract_specific_data(file_path, sheet_name=0, target_columns=None):
    """提取特定数据并处理"""
    df = read_excel_columns(file_path, sheet_name, target_columns)
    
    if df is not None:
        print("\n提取的数据:")
        print(df.head(10))  # 显示前10行
        
        # 基本统计信息
        print(f"\n数据统计:")
        print(f"总行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查空值
        print(f"\n空值统计:")
        print(df.isnull().sum())
        
        return df
    return None

def show_chenzhou_report(file_path):
    """显示郴州市工作表中的单位和不达标指数数据"""
    try:
        # 先读取郴州市工作表查看所有列名
        df_all = pd.read_excel(file_path, sheet_name='郴州市', nrows=0)
        print("=== 郴州市工作表可用列名 ===")
        for i, col in enumerate(df_all.columns):
            print(f"  {i}: {col}")
        
        # 尝试读取所有数据，然后手动选择相关列
        df = pd.read_excel(file_path, sheet_name='郴州市')
        
        print(f"\n=== 郴州市工作表数据预览 ===")
        print(df.head())
        
        # 尝试找到包含"单位"或类似字段的列
        unit_cols = [col for col in df.columns if '单位' in str(col) or '部门' in str(col) or '机构' in str(col)]
        index_cols = [col for col in df.columns if '指数' in str(col) or '不达标' in str(col)]
        
        if unit_cols:
            print(f"\n找到可能的单位列: {unit_cols}")
        if index_cols:
            print(f"找到可能的指数列: {index_cols}")
            
        return df
        
    except FileNotFoundError:
        print(f"文件不存在: {file_path}")
    except ValueError as e:
        if "Worksheet named '郴州市' not found" in str(e):
            print("错误: 找不到名为'郴州市'的工作表")
            print("请先运行功能1查看可用的工作表名称")
        else:
            print(f"读取错误: {e}")
    except Exception as e:
        print(f"读取文件出错: {e}")
    
    return None

def main():
    file_path = r"c:\Users\<USER>\Documents\augment-projects\dy\抖音指数每日通报（定）7.29.xlsx"
    
    print("=== Excel文件分析工具 ===")
    print("1. 分析文件结构")
    print("2. 读取指定列数据")
    print("3. 显示郴州市单位不达标指数")
    print("4. 退出")
    
    while True:
        choice = input("\n请选择功能: ").strip()
        
        if choice == '1':
            analyze_excel_structure(file_path)
            
        elif choice == '2':
            print("\n请指定要读取的参数:")
            
            # 工作表选择
            sheet_input = input("工作表名称或索引 (默认0): ").strip()
            sheet_name = int(sheet_input) if sheet_input.isdigit() else (sheet_input if sheet_input else 0)
            
            # 列选择
            columns_input = input("列名或列索引 (用逗号分隔，留空读取所有列): ").strip()
            if columns_input:
                # 尝试解析为数字索引或列名
                columns = []
                for col in columns_input.split(','):
                    col = col.strip()
                    if col.isdigit():
                        columns.append(int(col))
                    else:
                        columns.append(col)
            else:
                columns = None
            
            # 读取数据
            df = extract_specific_data(file_path, sheet_name, columns)
            
            if df is not None:
                # 询问是否保存
                save_choice = input("\n是否保存到新文件? (y/n): ").strip().lower()
                if save_choice == 'y':
                    output_path = input("输出文件路径 (默认: output.xlsx): ").strip()
                    if not output_path:
                        output_path = "output.xlsx"
                    
                    try:
                        df.to_excel(output_path, index=False)
                        print(f"数据已保存到: {output_path}")
                    except Exception as e:
                        print(f"保存失败: {e}")
                        
        elif choice == '3':
            show_chenzhou_report(file_path)
            
        elif choice == '4':
            print("程序已退出")
            break
            
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()




